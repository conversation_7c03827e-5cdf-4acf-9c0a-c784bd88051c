<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use <PERSON><PERSON>\Jetstream\Events\TeamCreated;
use <PERSON><PERSON>\Jetstream\Events\TeamDeleted;
use <PERSON><PERSON>\Jetstream\Events\TeamUpdated;
use <PERSON><PERSON>\Jetstream\Team as JetstreamTeam;

class Team extends JetstreamTeam
{
    use HasFactory;

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'personal_team' => 'boolean',
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var string<int, string>
     */
    protected $fillable = [
        'name',
        'personal_team',
    ];

    /**
     * The event map for the model.
     *
     * @var array<string, class-string>
     */
    protected $dispatchesEvents = [
        'created' => TeamCreated::class,
        'updated' => TeamUpdated::class,
        'deleted' => TeamDeleted::class,
    ];

    public function bots()
    {
        return $this->hasMany(Bot::class);
    }

    public function knowledges()
    {
        return $this->hasMany(Knowledge::class);
    }

    public function channels()
    {
        return $this->hasMany(Channel::class);
    }

    public function transactions()
    {
        return $this->hasMany(Transaction::class);
    }

    public function balance()
    {
        return $this->hasOne(Balance::class)->withDefault([
            'amount' => 0,
        ]);
    }
}
