<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class EarlyAccessStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'organization_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'website' => 'nullable|string|max:255',
            'organization_type' => 'required|string|in:school,social,business,other',
            'description' => 'required|string|max:1000',
        ];
    }
}
