{"name": "herobot.id/herobot", "version": "1.0", "type": "project", "description": "The Herobot Enterprise Application", "keywords": ["herobot"], "license": "BSD-3-<PERSON><PERSON>", "require": {"php": "^8.4", "guzzlehttp/guzzle": "^7.2", "inertiajs/inertia-laravel": "^1.0", "laravel/framework": "^12.0", "laravel/jetstream": "^5.0", "laravel/reverb": "^1.4", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.7", "openai-php/laravel": "^0.13.0", "tightenco/ziggy": "^2.3", "xendit/xendit-php": "^6.0"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}