services:
    laravel.test:
        build:
            context: ./docker/sail
            dockerfile: Dockerfile
            args:
                WWWUSER: '${WWWUSER:-1000}'
                WWWGROUP: '${WWWGROUP:-1000}'
        image: sail-8.3/app
        extra_hosts:
            - 'host.docker.internal:host-gateway'
        ports:
            - '${APP_PORT:-80}:80'
            - '${VITE_PORT:-5173}:${VITE_PORT:-5173}'
            - '${REVERB_SERVER_PORT:-8080}:${REVERB_SERVER_PORT:-8080}'
        environment:
            WWWUSER: '${WWWUSER:-1000}'
            LARAVEL_SAIL: 1
            XDEBUG_MODE: '${SAIL_XDEBUG_MODE:-off}'
            XDEBUG_CONFIG: '${SAIL_XDEBUG_CONFIG:-client_host=host.docker.internal}'
            IGNITION_LOCAL_SITES_PATH: '${PWD}'
            SUPERVISOR_PHP_USER: '${SUPERVISOR_PHP_USER:-sail}'
        volumes:
            - '.:/var/www/html'
        networks:
            - sail
        depends_on:
            - mariadb
    mariadb:
        image: 'mariadb:10'
        ports:
            - '${FORWARD_DB_PORT:-3306}:3306'
        environment:
            MYSQL_ROOT_PASSWORD: '${DB_PASSWORD}'
            MYSQL_ROOT_HOST: '%'
            MYSQL_DATABASE: '${DB_DATABASE}'
            MYSQL_USER: '${DB_USERNAME}'
            MYSQL_PASSWORD: '${DB_PASSWORD}'
            MYSQL_ALLOW_EMPTY_PASSWORD: 'yes'
            WA_DB_DATABASE: '${WA_DB_DATABASE:-whatsapp}'
            WA_DB_TABLE_NAME: '${WA_DB_TABLE_NAME:-auth}'
        volumes:
            - './data/mysql:/var/lib/mysql'
            - './docker/mysql/create-testing-database.sh:/docker-entrypoint-initdb.d/10-create-testing-database.sh'
            - './docker/mysql/create-whatsapp-database.sh:/docker-entrypoint-initdb.d/20-create-whatsapp-database.sh'
        networks:
            - sail
        healthcheck:
            test:
                - CMD
                - mysqladmin
                - ping
                - '-p${DB_PASSWORD}'
            retries: 3
            timeout: 5s
networks:
    sail:
        driver: bridge
