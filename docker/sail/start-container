#!/usr/bin/env bash

if [ "$SUPERVISOR_PHP_USER" != "root" ] && [ "$SUPERVISOR_PHP_USER" != "sail" ]; then
    echo "You should set SUPERVISOR_PHP_USER to either 'sail' or 'root'."
    exit 1
fi

if [ ! -z "$WWWUSER" ]; then
    usermod -u $WWWUSER sail
fi

if [ ! -d /.composer ]; then
    mkdir /.composer
fi

chmod -R ugo+rw /.composer

# Fix line endings in .env files if they exist
if [ -f .env ]; then
    sed -i 's/\r$//' .env
fi
if [ -f .env.example ]; then
    sed -i 's/\r$//' .env.example
fi

# Run composer install first
if [ "$SUPERVISOR_PHP_USER" = "root" ]; then
    composer install
else
    gosu $WWWUSER composer install
fi

# Run npm install
echo "Installing NPM dependencies..."
if [ "$SUPERVISOR_PHP_USER" = "root" ]; then
    npm install
else
    gosu $WWWUSER npm install
fi

# Generate application key
echo "Check APP_KEY..."
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
fi
if [ -z "$APP_KEY" ]; then
    echo "there is no APP_KEY. run php artisan key:generate..."
    
    if ["$SUPERVISOR_PHP_USER" = "root"]; then
        php artisan key:generate
    else
        gosu "${WWWUSER}" php artisan key:generate
    fi
else
    echo "APP_KEY already exists!"
fi


# Run migrations and seeders
echo "Running migrations and seeders..."
if [ "$SUPERVISOR_PHP_USER" = "root" ]; then
    php artisan migrate --seed
else
    gosu $WWWUSER php artisan migrate --seed
fi

if [ $# -gt 0 ]; then
    if [ "$SUPERVISOR_PHP_USER" = "root" ]; then
        exec "$@"
    else
        exec gosu $WWWUSER "$@"
    fi
else
    exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
fi
