<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $user = \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'User Example',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'remember_token' => \Illuminate\Support\Str::random(10),
            ]
        );

        \App\Models\Team::firstOrCreate(
            ['name' => 'User Example\'s Team'],
            ['user_id' => $user->id, 'personal_team' => true]
        );
    }
}
