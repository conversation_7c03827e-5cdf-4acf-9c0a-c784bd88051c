---
description:
globs:
alwaysApply: false
---
# WhatsApp Integration (Baileys)

## WhatsApp Server
The application includes a separate Node.js server for WhatsApp functionality using <PERSON><PERSON> library.

### Key Dependencies
- **@whiskeysockets/baileys**: WhatsApp Web API implementation
- **mysql-baileys**: MySQL adapter for Baileys sessions
- **qrcode**: QR code generation for device pairing
- **mysql2**: MySQL database connection

### WhatsApp Server Structure
- `whatsapp-server/` - Dedicated Node.js server for WhatsApp bot
- Express.js server handling WhatsApp Web API
- MySQL integration for session management
- QR code authentication flow

## Integration Points

### Laravel ↔ WhatsApp Server
- <PERSON><PERSON> handles web interface and business logic
- WhatsApp server handles message processing and bot responses
- Database shared between Laravel and WhatsApp server for:
  - User sessions
  - Message history
  - Bot configurations
  - Contact management

### Real-time Communication
- **Laravel Reverb**: WebSocket server for real-time updates
- **<PERSON>vel Echo**: Client-side WebSocket handling in [resources/js/echo.js](mdc:resources/js/echo.js)
- **Pusher Protocol**: Compatible broadcasting for instant updates

## Bot Features
Based on the Herobot name, this application likely includes:
- Automated message responses
- Contact management
- Message broadcasting
- Analytics and reporting
- Multi-device WhatsApp support

## Authentication Flow
1. Generate QR code via WhatsApp server
2. Scan QR code with WhatsApp mobile app
3. Store session data in MySQL via mysql-baileys
4. Real-time status updates via Laravel Reverb

## Development Considerations
- WhatsApp server runs independently from Laravel
- Both services must be running for full functionality
- Session management is crucial for WhatsApp connectivity
- Rate limiting and compliance with WhatsApp terms of service
