---
description:
globs:
alwaysApply: false
---

# Laravel Application Structure

## Directory Organization
- `app/` - Core application code (Models, Controllers, Services)
- `config/` - Configuration files
- `database/` - Migrations, seeders, and factories
- `routes/` - Route definitions (web, api, console, channels)
- `resources/` - Views, frontend assets, language files
- `storage/` - Logs, cache, sessions, uploads
- `tests/` - PHPUnit tests
- `bootstrap/` - Application bootstrap files
- `public/` - Web root, compiled assets

## Key Laravel Files
- [artisan](mdc:artisan) - Command-line interface
- [composer.json](mdc:composer.json) - PHP dependencies and autoloading
- [phpunit.xml](mdc:phpunit.xml) - Testing configuration

## Development Commands
- `php artisan serve` - Start development server
- `php artisan migrate` - Run database migrations
- `php artisan tinker` - Interactive PHP REPL
- `php artisan make:*` - Generate boilerplate code
- `composer install` - Install PHP dependencies

## Laravel Features Used
- **Jetstream**: Authentication scaffolding with teams
- **Sanctum**: API token authentication
- **Inertia.js**: Modern monolith approach
- **Reverb**: Real-time WebSocket broadcasting
- **Tinker**: Interactive debugging and testing
