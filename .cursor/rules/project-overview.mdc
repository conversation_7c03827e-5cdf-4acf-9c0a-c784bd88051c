---
description:
globs:
alwaysApply: false
---
# Herobot Enterprise Application - Project Overview

This is a Laravel 12 application with Inertia.js and Vue 3 frontend, designed for WhatsApp automation and bot management.

## Tech Stack
- **Backend**: Laravel 12 (PHP 8.4+)
- **Frontend**: Vue 3 + Inertia.js + Tailwind CSS
- **Database**: MySQL (with Bailey<PERSON> for WhatsApp)
- **Build Tool**: Vite
- **Authentication**: Laravel Jetstream + Sanctum
- **Real-time**: Laravel Reverb (WebSockets)
- **AI Integration**: OpenAI PHP Laravel package
- **Payment**: Xendit integration

## Key Entry Points
- Laravel entry point: [artisan](mdc:artisan)
- Frontend entry point: [resources/js/app.js](mdc:resources/js/app.js)
- Configuration: [composer.json](mdc:composer.json) and [package.json](mdc:package.json)
- Vite config: [vite.config.js](mdc:vite.config.js)
- Tailwind config: [tailwind.config.js](mdc:tailwind.config.js)

## Architecture
This is a full-stack application using:
- Laravel as API backend with Inertia.js for SPA-like experience
- Vue 3 components for interactive UI
- WhatsApp integration via Baileys library
- Docker containerization support via [docker-compose.yml](mdc:docker-compose.yml)
