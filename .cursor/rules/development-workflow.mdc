---
description: 
globs: 
alwaysApply: false
---
# Development Workflow & Best Practices

## Getting Started
1. **Backend Setup**:
   ```bash
   composer install
   cp .env.example .env
   php artisan key:generate
   php artisan migrate
   ```

2. **Frontend Setup**:
   ```bash
   npm install
   npm run dev
   ```

3. **Docker Development**:
   ```bash
   docker-compose up -d
   ```

## Common Development Commands

### Laravel Commands
- `php artisan serve` - Start development server (port 8000)
- `php artisan migrate:fresh --seed` - Reset database with seeders
- `php artisan make:controller ControllerName`
- `php artisan make:model ModelName -m` - Create model with migration
- `php artisan route:list` - Show all routes
- `php artisan config:cache` - Cache configuration

### Frontend Commands
- `npm run dev` - Development server with hot reload
- `npm run build` - Production build
- `npm run build && php artisan serve` - Test production build locally

### Testing
- `php artisan test` - Run PHPUnit tests
- `./vendor/bin/phpunit` - Alternative test runner

## File Organization Patterns

### Laravel MVC Pattern
- **Controllers**: Handle HTTP requests and responses
- **Models**: Database interactions and business logic
- **Views**: Blade templates (minimal with Inertia.js)
- **Middleware**: Request filtering and modification

### Inertia.js Patterns
- **Pages**: Full page components in `resources/js/Pages/`
- **Components**: Reusable UI components in `resources/js/Components/`
- **Layouts**: Page layouts in `resources/js/Layouts/`
- **Shared Data**: Global data accessible to all pages

## Environment Configuration
- [.env.example](mdc:.env.example) - Environment template
- Local development typically uses SQLite or MySQL
- Production uses MySQL with proper caching (Redis recommended)

## Docker Services
- **PHP/Laravel**: Main application container
- **MySQL**: Database service
- **Node.js**: For WhatsApp server integration
- **Redis**: Caching and sessions (optional but recommended)
