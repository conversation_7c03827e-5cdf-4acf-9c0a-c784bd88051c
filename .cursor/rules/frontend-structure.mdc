---
description:
globs:
alwaysApply: false
---
# Frontend Structure (Vue 3 + Inertia.js)

## Key Frontend Files
- [resources/js/app.js](mdc:resources/js/app.js) - Main Vue application entry point
- [resources/js/bootstrap.js](mdc:resources/js/bootstrap.js) - Bootstrap configuration and Echo setup
- [vite.config.js](mdc:vite.config.js) - Vite build configuration
- [package.json](mdc:package.json) - Node.js dependencies and scripts
- [tailwind.config.js](mdc:tailwind.config.js) - Tailwind CSS configuration

## Frontend Directory Structure
- `resources/js/Pages/` - Inertia.js page components
- `resources/js/Components/` - Reusable Vue components
- `resources/js/Layouts/` - Layout components
- `resources/js/Assets/` - Static assets
- `resources/css/` - Stylesheets
- `resources/views/` - Blade templates (mainly app.blade.php for Inertia)

## Frontend Tech Stack
- **Vue 3**: Progressive JavaScript framework
- **Inertia.js**: Modern monolith approach (no API needed)
- **Tailwind CSS**: Utility-first CSS framework
- **Headless UI**: Unstyled, accessible UI components
- **Heroicons**: SVG icon library
- **Chart.js**: Data visualization
- **Vite**: Fast build tool and dev server

## Development Commands
- `npm run dev` - Start Vite development server with hot reload
- `npm run build` - Build for production
- `npm install` - Install Node.js dependencies

## Real-time Features
- **Laravel Echo**: WebSocket client integration via [resources/js/echo.js](mdc:resources/js/echo.js)
- **Pusher**: Real-time broadcasting (configured for Laravel Reverb)

## WhatsApp Integration
- **Baileys**: WhatsApp Web API library
- **QR Code**: QR code generation for WhatsApp authentication
- Express server for WhatsApp bot handling (see `whatsapp-server/` directory)
